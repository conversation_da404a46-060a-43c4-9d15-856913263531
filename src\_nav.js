import React from "react";
import CIcon from "@coreui/icons-react";
import {
  cilBell,
  // cilAvTimer,
  // cilCalculator,
  // cilChart<PERSON>ie,
  // cilCursor,
  // cilDescription,
  // cilDrop,
  cilNotes,
  // cilPencil,
  // cilCalendar,
  // cilUser,
  // cilPuzzle,
  // cilSpeedometer,
  // cilStar,
  cilHeadphones,
  // cilChartLine,
  // cilMoney,
} from "@coreui/icons";
import { CNavItem, CNavTitle } from "@coreui/react";
import { MdOutlineCategory, MdOutlineMessage } from "react-icons/md";
import {
  FaBagShopping,
  FaQuestion,
  FaUsers,
  FaUserXmark,
} from "react-icons/fa6";
import { FaBook } from "react-icons/fa6";
import { MdOutlinePets } from "react-icons/md";
import { IoCreate } from "react-icons/io5";

import { PiFlagBannerFill } from "react-icons/pi";

const _nav = [
  // {
  //   component: CNavTitle,
  //   name: 'Posts Management',
  // },
  // {
  //   component: CNavItem,
  //   name: 'Categories',
  //   to: '/categories',
  //   icon: <MdOutlineCategory height={64} width={64} className="nav-icon" />,
  // },
  // {
  //   component: CNavItem,
  //   name: 'Posts',
  //   to: '/posts',
  //   icon: <FaBagShopping height={64} width={64} className="nav-icon" />,
  // },
  // {
  //   component: CNavItem,
  //   name: 'Ads',
  //   to: '/ads',
  //   icon: <PiFlagBannerFill height={64} width={64} className="nav-icon" />,
  // },
  // {
  //   component: CNavItem,
  //   name: 'Feedback',
  //   to: '/feedback',
  //   icon: <MdOutlineMessage height={64} width={64} className="nav-icon" />,
  // },
  // {
  //   component: CNavItem,
  //   name: 'FAQs',
  //   to: '/faqs',
  //   icon: <FaQuestion height={64} width={64} className="nav-icon" />,
  // },
  {
    component: CNavTitle,
    name: "Users Management",
  },
  {
    component: CNavItem,
    name: "Users",
    to: "/users",
    icon: <FaUsers height={64} width={64} className="nav-icon" />,
  },
  {
    component: CNavItem,
    name: "Projects",
    to: "/projects",
    icon: <IoCreate height={64} width={64} className="nav-icon" />,
  },

  {
    component: CNavItem,
    name: "Membership Requests",
    to: "/membershipRequests",
    icon: <FaUserXmark height={64} width={64} className="nav-icon" />,
  },
  {
    component: CNavItem,
    name: "Delete Requests",
    to: "/requests",
    icon: <FaUserXmark height={64} width={64} className="nav-icon" />,
  },

  // {
  //   component: CNavTitle,
  //   name: 'Notifications',
  // },
  // {
  //   component: CNavItem,
  //   name: 'Send Notification',
  //   to: '/send-notifications',
  //   icon: <CIcon icon={cilBell} customClassName="nav-icon" />,
  // },
  // {
  //   component: CNavItem,
  //   name: 'Schedule Notification',
  //   to: '/schedule-notifications',
  //   icon: <CIcon icon={cilAvTimer} customClassName="nav-icon" />,
  // },
  // {
  //   component: CNavTitle,
  //   name: 'Support',
  // },

  // {
  //   component: CNavItem,
  //   name: 'Customer Service',
  //   to: '/chat',
  //   icon: <CIcon icon={cilNotes} customClassName="nav-icon" />,
  // },
  // {
  //   component: CNavItem,
  //   name: 'Privacy Policy',
  //   to: '/privacy',
  //   icon: <CIcon icon={cilNotes} customClassName="nav-icon" />,
  // },
  // {
  //   component: CNavItem,
  //   name: 'Contact Us',
  //   to: '/contact-us',
  //   icon: <CIcon icon={cilHeadphones} customClassName="nav-icon" />,
  // },
  // {
  //   component: CNavGroup,
  //   name: 'Base',
  //   to: '/base',
  //   icon: <CIcon icon={cilPuzzle} customClassName="nav-icon" />,
  //   items: [
  //     {
  //       component: CNavItem,
  //       name: 'Accordion',
  //       to: '/base/accordion',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Breadcrumb',
  //       to: '/base/breadcrumbs',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Cards',
  //       to: '/base/cards',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Carousel',
  //       to: '/base/carousels',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Collapse',
  //       to: '/base/collapses',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'List group',
  //       to: '/base/list-groups',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Navs & Tabs',
  //       to: '/base/navs',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Pagination',
  //       to: '/base/paginations',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Placeholders',
  //       to: '/base/placeholders',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Popovers',
  //       to: '/base/popovers',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Progress',
  //       to: '/base/progress',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Spinners',
  //       to: '/base/spinners',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Tables',
  //       to: '/base/tables',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Tooltips',
  //       to: '/base/tooltips',
  //     },
  //   ],
  // },
  // {
  //   component: CNavGroup,
  //   name: 'Buttons',
  //   to: '/buttons',
  //   icon: <CIcon icon={cilCursor} customClassName="nav-icon" />,
  //   items: [
  //     {
  //       component: CNavItem,
  //       name: 'Buttons',
  //       to: '/buttons/buttons',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Buttons groups',
  //       to: '/buttons/button-groups',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Dropdowns',
  //       to: '/buttons/dropdowns',
  //     },
  //   ],
  // },
  // {
  //   component: CNavGroup,
  //   name: 'Forms',
  //   icon: <CIcon icon={cilNotes} customClassName="nav-icon" />,
  //   items: [
  //     {
  //       component: CNavItem,
  //       name: 'Form Control',
  //       to: '/forms/form-control',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Select',
  //       to: '/forms/select',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Checks & Radios',
  //       to: '/forms/checks-radios',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Range',
  //       to: '/forms/range',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Input Group',
  //       to: '/forms/input-group',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Floating Labels',
  //       to: '/forms/floating-labels',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Layout',
  //       to: '/forms/layout',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Validation',
  //       to: '/forms/validation',
  //     },
  //   ],
  // },
  // {
  //   component: CNavItem,
  //   name: 'Charts',
  //   to: '/charts',
  //   icon: <CIcon icon={cilChartPie} customClassName="nav-icon" />,
  // },
  // {
  //   component: CNavGroup,
  //   name: 'Icons',
  //   icon: <CIcon icon={cilStar} customClassName="nav-icon" />,
  //   items: [
  //     {
  //       component: CNavItem,
  //       name: 'CoreUI Free',
  //       to: '/icons/coreui-icons',
  //       badge: {
  //         color: 'success',
  //         text: 'NEW',
  //       },
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'CoreUI Flags',
  //       to: '/icons/flags',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'CoreUI Brands',
  //       to: '/icons/brands',
  //     },
  //   ],
  // },
  // {
  //   component: CNavGroup,
  //   name: 'Notifications',
  //   icon: <CIcon icon={cilBell} customClassName="nav-icon" />,
  //   items: [
  //     {
  //       component: CNavItem,
  //       name: 'Alerts',
  //       to: '/notifications/alerts',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Badges',
  //       to: '/notifications/badges',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Modal',
  //       to: '/notifications/modals',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Toasts',
  //       to: '/notifications/toasts',
  //     },
  //   ],
  // },
  // {
  //   component: CNavItem,
  //   name: 'Widgets',
  //   to: '/widgets',
  //   icon: <CIcon icon={cilCalculator} customClassName="nav-icon" />,
  //   badge: {
  //     color: 'info',
  //     text: 'NEW',
  //   },
  // },
  // {
  //   component: CNavTitle,
  //   name: 'Extras',
  // },
  // {
  //   component: CNavGroup,
  //   name: 'Pages',
  //   icon: <CIcon icon={cilStar} customClassName="nav-icon" />,
  //   items: [
  //     {
  //       component: CNavItem,
  //       name: 'Login',
  //       to: '/login',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Register',
  //       to: '/register',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Error 404',
  //       to: '/404',
  //     },
  //     {
  //       component: CNavItem,
  //       name: 'Error 500',
  //       to: '/500',
  //     },
  //   ],
  // },
  // {
  //   component: CNavItem,
  //   name: 'Docs',
  //   href: 'https://coreui.io/react/docs/templates/installation/',
  //   icon: <CIcon icon={cilDescription} customClassName="nav-icon" />,
  // },
];

export default _nav;
